package cloudflare

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"cdn-manager/internal/config"
	"cdn-manager/internal/models"

	"github.com/cloudflare/cloudflare-go"
)

// CloudflareService Cloudflare服务
type CloudflareService struct {
	api    *cloudflare.API
	config config.CloudflareConfig
}

// NewCloudflareService 创建Cloudflare服务实例
func NewCloudflareService(cfg config.CloudflareConfig) (*CloudflareService, error) {
	var api *cloudflare.API
	var err error

	if cfg.APIToken != "" {
		api, err = cloudflare.NewWithAPIToken(cfg.APIToken)
	} else if cfg.Email != "" && cfg.APIKey != "" {
		api, err = cloudflare.New(cfg.APIKey, cfg.Email)
	} else {
		return nil, fmt.Errorf("either API token or email+API key must be provided")
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create cloudflare client: %v", err)
	}

	return &CloudflareService{
		api:    api,
		config: cfg,
	}, nil
}

// ZoneInfo Zone信息
type ZoneInfo struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Status      string   `json:"status"`
	NameServers []string `json:"name_servers"`
	Plan        string   `json:"plan"`
}

// ListZones 获取Zone列表
func (s *CloudflareService) ListZones() ([]ZoneInfo, error) {
	ctx := context.Background()
	zones, err := s.api.ListZones(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list zones: %v", err)
	}

	var zoneInfos []ZoneInfo
	for _, zone := range zones {
		zoneInfo := ZoneInfo{
			ID:          zone.ID,
			Name:        zone.Name,
			Status:      zone.Status,
			NameServers: zone.NameServers,
			Plan:        zone.Plan.Name,
		}
		zoneInfos = append(zoneInfos, zoneInfo)
	}

	return zoneInfos, nil
}

// CreateZone 创建Zone
func (s *CloudflareService) CreateZone(domain string) (*ZoneInfo, error) {
	ctx := context.Background()
	
	zone, err := s.api.CreateZone(ctx, domain, false, cloudflare.Account{}, "full")
	if err != nil {
		return nil, fmt.Errorf("failed to create zone: %v", err)
	}

	return &ZoneInfo{
		ID:          zone.ID,
		Name:        zone.Name,
		Status:      zone.Status,
		NameServers: zone.NameServers,
		Plan:        zone.Plan.Name,
	}, nil
}

// DeleteZone 删除Zone
func (s *CloudflareService) DeleteZone(zoneID string) error {
	ctx := context.Background()
	
	_, err := s.api.DeleteZone(ctx, zoneID)
	if err != nil {
		return fmt.Errorf("failed to delete zone: %v", err)
	}

	return nil
}

// PageRule 页面规则
type PageRule struct {
	ID       string                 `json:"id"`
	Pattern  string                 `json:"pattern"`
	Actions  map[string]interface{} `json:"actions"`
	Priority int                    `json:"priority"`
	Status   string                 `json:"status"`
}

// GetPageRules 获取页面规则
func (s *CloudflareService) GetPageRules(zoneID string) ([]PageRule, error) {
	ctx := context.Background()
	
	rules, err := s.api.ListPageRules(ctx, zoneID)
	if err != nil {
		return nil, fmt.Errorf("failed to get page rules: %v", err)
	}

	var pageRules []PageRule
	for _, rule := range rules {
		pageRule := PageRule{
			ID:       rule.ID,
			Pattern:  rule.Targets[0].Constraint.Value,
			Actions:  make(map[string]interface{}),
			Priority: rule.Priority,
			Status:   rule.Status,
		}

		// 转换动作
		for _, action := range rule.Actions {
			pageRule.Actions[action.ID] = action.Value
		}

		pageRules = append(pageRules, pageRule)
	}

	return pageRules, nil
}

// CreatePageRule 创建页面规则
func (s *CloudflareService) CreatePageRule(zoneID, pattern string, actions map[string]interface{}) (*PageRule, error) {
	ctx := context.Background()
	
	var cfActions []cloudflare.PageRuleAction
	for key, value := range actions {
		cfActions = append(cfActions, cloudflare.PageRuleAction{
			ID:    key,
			Value: value,
		})
	}

	rule := cloudflare.PageRule{
		Targets: []cloudflare.PageRuleTarget{
			{
				Target: "url",
				Constraint: struct {
					Operator string `json:"operator"`
					Value    string `json:"value"`
				}{
					Operator: "matches",
					Value:    pattern,
				},
			},
		},
		Actions: cfActions,
		Status:  "active",
	}

	createdRule, err := s.api.CreatePageRule(ctx, zoneID, rule)
	if err != nil {
		return nil, fmt.Errorf("failed to create page rule: %v", err)
	}

	return &PageRule{
		ID:       createdRule.ID,
		Pattern:  pattern,
		Actions:  actions,
		Priority: createdRule.Priority,
		Status:   createdRule.Status,
	}, nil
}

// PurgeCache 刷新缓存
func (s *CloudflareService) PurgeCache(zoneID string, urls []string, purgeType string) (string, error) {
	ctx := context.Background()
	
	var purgeRequest cloudflare.PurgeCacheRequest
	
	switch purgeType {
	case "everything":
		purgeRequest.Everything = true
	case "files":
		purgeRequest.Files = urls
	case "tags":
		purgeRequest.Tags = urls
	case "hosts":
		purgeRequest.Hosts = urls
	default:
		return "", fmt.Errorf("unsupported purge type: %s", purgeType)
	}

	response, err := s.api.PurgeCache(ctx, zoneID, purgeRequest)
	if err != nil {
		return "", fmt.Errorf("failed to purge cache: %v", err)
	}

	return response.ID, nil
}

// ZoneSettings Zone设置
type ZoneSettings struct {
	CacheLevel       string `json:"cache_level"`
	BrowserCacheTTL  int    `json:"browser_cache_ttl"`
	EdgeCacheTTL     int    `json:"edge_cache_ttl"`
	AlwaysOnline     string `json:"always_online"`
	DevelopmentMode  string `json:"development_mode"`
	SecurityLevel    string `json:"security_level"`
}

// GetZoneSettings 获取Zone设置
func (s *CloudflareService) GetZoneSettings(zoneID string) (*ZoneSettings, error) {
	ctx := context.Background()
	
	settings, err := s.api.ZoneSettings(ctx, zoneID)
	if err != nil {
		return nil, fmt.Errorf("failed to get zone settings: %v", err)
	}

	zoneSettings := &ZoneSettings{}
	
	for _, setting := range settings.Result {
		switch setting.ID {
		case "cache_level":
			if val, ok := setting.Value.(string); ok {
				zoneSettings.CacheLevel = val
			}
		case "browser_cache_ttl":
			if val, ok := setting.Value.(float64); ok {
				zoneSettings.BrowserCacheTTL = int(val)
			}
		case "edge_cache_ttl":
			if val, ok := setting.Value.(float64); ok {
				zoneSettings.EdgeCacheTTL = int(val)
			}
		case "always_online":
			if val, ok := setting.Value.(string); ok {
				zoneSettings.AlwaysOnline = val
			}
		case "development_mode":
			if val, ok := setting.Value.(string); ok {
				zoneSettings.DevelopmentMode = val
			}
		case "security_level":
			if val, ok := setting.Value.(string); ok {
				zoneSettings.SecurityLevel = val
			}
		}
	}

	return zoneSettings, nil
}

// UpdateZoneSetting 更新Zone设置
func (s *CloudflareService) UpdateZoneSetting(zoneID, settingID string, value interface{}) error {
	ctx := context.Background()
	
	_, err := s.api.UpdateZoneSetting(ctx, zoneID, settingID, value)
	if err != nil {
		return fmt.Errorf("failed to update zone setting: %v", err)
	}

	return nil
}

// AnalyticsData 分析数据
type AnalyticsData struct {
	Date      time.Time `json:"date"`
	Requests  int64     `json:"requests"`
	Bandwidth int64     `json:"bandwidth"`
	Threats   int64     `json:"threats"`
	PageViews int64     `json:"page_views"`
	Uniques   int64     `json:"uniques"`
}

// GetAnalytics 获取分析数据
func (s *CloudflareService) GetAnalytics(zoneID string, startTime, endTime time.Time) ([]AnalyticsData, error) {
	ctx := context.Background()
	
	options := cloudflare.ZoneAnalyticsOptions{
		Since: &startTime,
		Until: &endTime,
	}

	analytics, err := s.api.ZoneAnalyticsDashboard(ctx, zoneID, options)
	if err != nil {
		return nil, fmt.Errorf("failed to get analytics: %v", err)
	}

	var data []AnalyticsData
	for _, item := range analytics.Timeseries {
		analyticsData := AnalyticsData{
			Date:      item.Since,
			Requests:  int64(item.Requests.All),
			Bandwidth: int64(item.Bandwidth.All),
			Threats:   int64(item.Threats.All),
			PageViews: int64(item.PageViews.All),
			Uniques:   int64(item.Uniques.All),
		}
		data = append(data, analyticsData)
	}

	return data, nil
}

// DNSRecord DNS记录
type DNSRecord struct {
	ID      string `json:"id"`
	Type    string `json:"type"`
	Name    string `json:"name"`
	Content string `json:"content"`
	TTL     int    `json:"ttl"`
	Proxied bool   `json:"proxied"`
}

// GetDNSRecords 获取DNS记录
func (s *CloudflareService) GetDNSRecords(zoneID string) ([]DNSRecord, error) {
	ctx := context.Background()
	
	records, err := s.api.DNSRecords(ctx, zoneID, cloudflare.DNSRecord{})
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS records: %v", err)
	}

	var dnsRecords []DNSRecord
	for _, record := range records {
		dnsRecord := DNSRecord{
			ID:      record.ID,
			Type:    record.Type,
			Name:    record.Name,
			Content: record.Content,
			TTL:     record.TTL,
			Proxied: *record.Proxied,
		}
		dnsRecords = append(dnsRecords, dnsRecord)
	}

	return dnsRecords, nil
}

// CreateDNSRecord 创建DNS记录
func (s *CloudflareService) CreateDNSRecord(zoneID string, record DNSRecord) (*DNSRecord, error) {
	ctx := context.Background()
	
	cfRecord := cloudflare.DNSRecord{
		Type:    record.Type,
		Name:    record.Name,
		Content: record.Content,
		TTL:     record.TTL,
		Proxied: &record.Proxied,
	}

	response, err := s.api.CreateDNSRecord(ctx, zoneID, cfRecord)
	if err != nil {
		return nil, fmt.Errorf("failed to create DNS record: %v", err)
	}

	return &DNSRecord{
		ID:      response.Result.ID,
		Type:    response.Result.Type,
		Name:    response.Result.Name,
		Content: response.Result.Content,
		TTL:     response.Result.TTL,
		Proxied: *response.Result.Proxied,
	}, nil
}

// SyncZoneToDB 同步Zone到数据库
func (s *CloudflareService) SyncZoneToDB(accountID uint, zone ZoneInfo) (*models.CDNDomain, error) {
	config, _ := json.Marshal(map[string]interface{}{
		"zone_id":      zone.ID,
		"name_servers": zone.NameServers,
		"plan":         zone.Plan,
	})

	cdnDomain := &models.CDNDomain{
		AccountID:  accountID,
		Domain:     zone.Name,
		Provider:   models.ProviderCloudflare,
		ProviderID: zone.ID,
		Status:     zone.Status,
		Config:     string(config),
		IsActive:   zone.Status == "active",
	}

	return cdnDomain, nil
}
