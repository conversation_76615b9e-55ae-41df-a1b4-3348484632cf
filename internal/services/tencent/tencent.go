package tencent

import (
	"encoding/json"
	"fmt"
	"time"

	"cdn-manager/internal/config"
	"cdn-manager/internal/models"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	teo "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/teo/v20220901"
)

// TencentService 腾讯云EdgeOne服务
type TencentService struct {
	client *teo.Client
	config config.TencentConfig
}

// NewTencentService 创建腾讯云服务实例
func NewTencentService(cfg config.TencentConfig) (*TencentService, error) {
	credential := common.NewCredential(cfg.SecretID, cfg.SecretKey)
	
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "teo.tencentcloudapi.com"
	
	client, err := teo.NewClient(credential, cfg.Region, cpf)
	if err != nil {
		return nil, fmt.Errorf("failed to create tencent client: %v", err)
	}

	return &TencentService{
		client: client,
		config: cfg,
	}, nil
}

// DomainInfo 域名信息
type DomainInfo struct {
	ZoneID     string `json:"zone_id"`
	ZoneName   string `json:"zone_name"`
	Status     string `json:"status"`
	CNAME      string `json:"cname"`
	NameServer []string `json:"name_server"`
}

// ListZones 获取站点列表
func (s *TencentService) ListZones() ([]DomainInfo, error) {
	request := teo.NewDescribeZonesRequest()
	
	response, err := s.client.DescribeZones(request)
	if err != nil {
		return nil, fmt.Errorf("failed to list zones: %v", err)
	}

	var domains []DomainInfo
	for _, zone := range response.Response.Zones {
		domain := DomainInfo{
			ZoneID:   *zone.ZoneId,
			ZoneName: *zone.ZoneName,
			Status:   *zone.Status,
		}
		
		if zone.CnameTarget != nil {
			domain.CNAME = *zone.CnameTarget
		}
		
		domains = append(domains, domain)
	}

	return domains, nil
}

// CreateZone 创建站点
func (s *TencentService) CreateZone(domain string) (*DomainInfo, error) {
	request := teo.NewCreateZoneRequest()
	request.ZoneName = common.StringPtr(domain)
	request.Type = common.StringPtr("full")

	response, err := s.client.CreateZone(request)
	if err != nil {
		return nil, fmt.Errorf("failed to create zone: %v", err)
	}

	return &DomainInfo{
		ZoneID:   *response.Response.ZoneId,
		ZoneName: domain,
		Status:   "pending",
	}, nil
}

// DeleteZone 删除站点
func (s *TencentService) DeleteZone(zoneID string) error {
	request := teo.NewDeleteZoneRequest()
	request.ZoneId = common.StringPtr(zoneID)

	_, err := s.client.DeleteZone(request)
	if err != nil {
		return fmt.Errorf("failed to delete zone: %v", err)
	}

	return nil
}

// CacheConfig 缓存配置
type CacheConfig struct {
	RuleID   string `json:"rule_id"`
	Pattern  string `json:"pattern"`
	TTL      int64  `json:"ttl"`
	Priority int64  `json:"priority"`
	Status   string `json:"status"`
}

// GetCacheRules 获取缓存规则
func (s *TencentService) GetCacheRules(zoneID string) ([]CacheConfig, error) {
	request := teo.NewDescribeRulesRequest()
	request.ZoneId = common.StringPtr(zoneID)

	response, err := s.client.DescribeRules(request)
	if err != nil {
		return nil, fmt.Errorf("failed to get cache rules: %v", err)
	}

	var rules []CacheConfig
	for _, rule := range response.Response.Rules {
		if rule.Actions != nil {
			for _, action := range rule.Actions {
				if *action.Action == "Cache" {
					cacheRule := CacheConfig{
						RuleID:   *rule.RuleId,
						Priority: *rule.Priority,
						Status:   *rule.Status,
					}
					
					// 解析条件和动作
					if len(rule.Conditions) > 0 {
						cacheRule.Pattern = *rule.Conditions[0].Values[0]
					}
					
					if action.Parameters != nil {
						for _, param := range action.Parameters {
							if *param.Name == "CacheTtl" {
								if ttl, ok := param.Values[0].(float64); ok {
									cacheRule.TTL = int64(ttl)
								}
							}
						}
					}
					
					rules = append(rules, cacheRule)
				}
			}
		}
	}

	return rules, nil
}

// CreateCacheRule 创建缓存规则
func (s *TencentService) CreateCacheRule(zoneID, pattern string, ttl int64) (*CacheConfig, error) {
	request := teo.NewCreateRuleRequest()
	request.ZoneId = common.StringPtr(zoneID)
	
	// 构建规则
	rule := &teo.Rule{
		Conditions: []*teo.RuleCondition{
			{
				Operator: common.StringPtr("equal"),
				Target:   common.StringPtr("file_extension"),
				Values:   []*string{common.StringPtr(pattern)},
			},
		},
		Actions: []*teo.RuleAction{
			{
				Action: common.StringPtr("Cache"),
				Parameters: []*teo.RuleActionParameter{
					{
						Name:   common.StringPtr("CacheTtl"),
						Values: []interface{}{ttl},
					},
				},
			},
		},
	}
	
	request.Rules = []*teo.Rule{rule}

	response, err := s.client.CreateRule(request)
	if err != nil {
		return nil, fmt.Errorf("failed to create cache rule: %v", err)
	}

	return &CacheConfig{
		RuleID:  *response.Response.RuleId,
		Pattern: pattern,
		TTL:     ttl,
		Status:  "active",
	}, nil
}

// PurgeCache 刷新缓存
func (s *TencentService) PurgeCache(zoneID string, urls []string, purgeType string) (string, error) {
	request := teo.NewCreatePurgeTaskRequest()
	request.ZoneId = common.StringPtr(zoneID)
	request.Type = common.StringPtr(purgeType) // url, prefix, host, cache_tag
	
	var targets []*string
	for _, url := range urls {
		targets = append(targets, common.StringPtr(url))
	}
	request.Targets = targets

	response, err := s.client.CreatePurgeTask(request)
	if err != nil {
		return "", fmt.Errorf("failed to purge cache: %v", err)
	}

	return *response.Response.JobId, nil
}

// GetPurgeTaskStatus 获取刷新任务状态
func (s *TencentService) GetPurgeTaskStatus(jobID string) (string, error) {
	request := teo.NewDescribePurgeTasksRequest()
	request.JobId = common.StringPtr(jobID)

	response, err := s.client.DescribePurgeTasks(request)
	if err != nil {
		return "", fmt.Errorf("failed to get purge task status: %v", err)
	}

	if len(response.Response.Tasks) > 0 {
		return *response.Response.Tasks[0].Status, nil
	}

	return "unknown", nil
}

// StatsData 统计数据
type StatsData struct {
	Date      time.Time `json:"date"`
	Requests  int64     `json:"requests"`
	Bandwidth int64     `json:"bandwidth"`
	HitRate   float64   `json:"hit_rate"`
}

// GetStats 获取统计数据
func (s *TencentService) GetStats(zoneID string, startTime, endTime time.Time) ([]StatsData, error) {
	request := teo.NewDescribeTimingL7AnalysisDataRequest()
	request.ZoneIds = []*string{common.StringPtr(zoneID)}
	request.StartTime = common.StringPtr(startTime.Format("2006-01-02T15:04:05Z"))
	request.EndTime = common.StringPtr(endTime.Format("2006-01-02T15:04:05Z"))
	request.MetricNames = []*string{
		common.StringPtr("l7Flow_request"),
		common.StringPtr("l7Flow_flux"),
		common.StringPtr("l7Flow_hitRate"),
	}
	request.Interval = common.StringPtr("1d")

	response, err := s.client.DescribeTimingL7AnalysisData(request)
	if err != nil {
		return nil, fmt.Errorf("failed to get stats: %v", err)
	}

	var stats []StatsData
	if len(response.Response.Data) > 0 {
		for _, item := range response.Response.Data[0].TypeLoadTimeData {
			stat := StatsData{
				Date: time.Unix(*item.Time, 0),
			}
			
			// 解析不同指标的数据
			for _, data := range response.Response.Data {
				switch *data.MetricName {
				case "l7Flow_request":
					for i, timeData := range data.TypeLoadTimeData {
						if i < len(stats) {
							stats[i].Requests = int64(*timeData.Value)
						}
					}
				case "l7Flow_flux":
					for i, timeData := range data.TypeLoadTimeData {
						if i < len(stats) {
							stats[i].Bandwidth = int64(*timeData.Value)
						}
					}
				case "l7Flow_hitRate":
					for i, timeData := range data.TypeLoadTimeData {
						if i < len(stats) {
							stats[i].HitRate = *timeData.Value
						}
					}
				}
			}
			
			stats = append(stats, stat)
		}
	}

	return stats, nil
}

// SyncDomainToDB 同步域名到数据库
func (s *TencentService) SyncDomainToDB(accountID uint, domain DomainInfo) (*models.CDNDomain, error) {
	config, _ := json.Marshal(map[string]interface{}{
		"zone_id": domain.ZoneID,
		"cname":   domain.CNAME,
	})

	cdnDomain := &models.CDNDomain{
		AccountID:  accountID,
		Domain:     domain.ZoneName,
		Provider:   models.ProviderTencent,
		ProviderID: domain.ZoneID,
		Status:     domain.Status,
		CNAME:      domain.CNAME,
		Config:     string(config),
		IsActive:   true,
	}

	return cdnDomain, nil
}
