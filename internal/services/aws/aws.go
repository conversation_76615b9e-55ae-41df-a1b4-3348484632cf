package aws

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"cdn-manager/internal/config"
	"cdn-manager/internal/models"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsconfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/cloudfront"
	"github.com/aws/aws-sdk-go-v2/service/cloudfront/types"
	"github.com/aws/aws-sdk-go-v2/service/cloudwatchlogs"
)

// AWSService AWS CloudFront服务
type AWSService struct {
	client    *cloudfront.Client
	cwClient  *cloudwatchlogs.Client
	config    config.AWSConfig
}

// NewAWSService 创建AWS服务实例
func NewAWSService(cfg config.AWSConfig) (*AWSService, error) {
	awsCfg, err := awsconfig.LoadDefaultConfig(context.TODO(),
		awsconfig.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			cfg.AccessKeyID,
			cfg.SecretAccessKey,
			"",
		)),
		awsconfig.WithRegion(cfg.Region),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %v", err)
	}

	return &AWSService{
		client:   cloudfront.NewFromConfig(awsCfg),
		cwClient: cloudwatchlogs.NewFromConfig(awsCfg),
		config:   cfg,
	}, nil
}

// DistributionInfo 分发信息
type DistributionInfo struct {
	ID       string   `json:"id"`
	Domain   string   `json:"domain"`
	Status   string   `json:"status"`
	CNAME    []string `json:"cname"`
	Origin   string   `json:"origin"`
	Enabled  bool     `json:"enabled"`
}

// ListDistributions 获取分发列表
func (s *AWSService) ListDistributions() ([]DistributionInfo, error) {
	input := &cloudfront.ListDistributionsInput{}
	
	result, err := s.client.ListDistributions(context.TODO(), input)
	if err != nil {
		return nil, fmt.Errorf("failed to list distributions: %v", err)
	}

	var distributions []DistributionInfo
	if result.DistributionList != nil {
		for _, item := range result.DistributionList.Items {
			dist := DistributionInfo{
				ID:      *item.Id,
				Domain:  *item.DomainName,
				Status:  string(item.Status),
				Enabled: *item.Enabled,
			}

			// 获取CNAME
			if item.Aliases != nil {
				for _, alias := range item.Aliases.Items {
					dist.CNAME = append(dist.CNAME, alias)
				}
			}

			// 获取源站
			if len(item.Origins.Items) > 0 {
				dist.Origin = *item.Origins.Items[0].DomainName
			}

			distributions = append(distributions, dist)
		}
	}

	return distributions, nil
}

// CreateDistribution 创建分发
func (s *AWSService) CreateDistribution(domain, origin string) (*DistributionInfo, error) {
	callerReference := fmt.Sprintf("cdn-manager-%d", time.Now().Unix())
	
	input := &cloudfront.CreateDistributionInput{
		DistributionConfig: &types.DistributionConfig{
			CallerReference: &callerReference,
			Comment:         aws.String("Created by CDN Manager"),
			Enabled:         aws.Bool(true),
			Aliases: &types.Aliases{
				Quantity: aws.Int32(1),
				Items:    []string{domain},
			},
			Origins: &types.Origins{
				Quantity: aws.Int32(1),
				Items: []types.Origin{
					{
						Id:         aws.String("origin1"),
						DomainName: aws.String(origin),
						CustomOriginConfig: &types.CustomOriginConfig{
							HTTPPort:             aws.Int32(80),
							HTTPSPort:            aws.Int32(443),
							OriginProtocolPolicy: types.OriginProtocolPolicyHttpsOnly,
						},
					},
				},
			},
			DefaultCacheBehavior: &types.DefaultCacheBehavior{
				TargetOriginId:       aws.String("origin1"),
				ViewerProtocolPolicy: types.ViewerProtocolPolicyRedirectToHttps,
				MinTTL:               aws.Int64(0),
				ForwardedValues: &types.ForwardedValues{
					QueryString: aws.Bool(false),
					Cookies: &types.CookiePreference{
						Forward: types.ItemSelectionNone,
					},
				},
				TrustedSigners: &types.TrustedSigners{
					Enabled:  aws.Bool(false),
					Quantity: aws.Int32(0),
				},
			},
			PriceClass: types.PriceClassPriceClassAll,
		},
	}

	result, err := s.client.CreateDistribution(context.TODO(), input)
	if err != nil {
		return nil, fmt.Errorf("failed to create distribution: %v", err)
	}

	return &DistributionInfo{
		ID:      *result.Distribution.Id,
		Domain:  *result.Distribution.DomainName,
		Status:  string(result.Distribution.Status),
		CNAME:   []string{domain},
		Origin:  origin,
		Enabled: *result.Distribution.DistributionConfig.Enabled,
	}, nil
}

// DeleteDistribution 删除分发
func (s *AWSService) DeleteDistribution(distributionID string) error {
	// 首先获取分发配置
	getInput := &cloudfront.GetDistributionInput{
		Id: aws.String(distributionID),
	}
	
	getResult, err := s.client.GetDistribution(context.TODO(), getInput)
	if err != nil {
		return fmt.Errorf("failed to get distribution: %v", err)
	}

	// 禁用分发
	config := getResult.Distribution.DistributionConfig
	config.Enabled = aws.Bool(false)
	
	updateInput := &cloudfront.UpdateDistributionInput{
		Id:                 aws.String(distributionID),
		DistributionConfig: config,
		IfMatch:            getResult.ETag,
	}
	
	_, err = s.client.UpdateDistribution(context.TODO(), updateInput)
	if err != nil {
		return fmt.Errorf("failed to disable distribution: %v", err)
	}

	// 等待分发状态更新后再删除
	// 注意：实际生产环境中需要轮询检查状态
	time.Sleep(5 * time.Minute)

	// 删除分发
	deleteInput := &cloudfront.DeleteDistributionInput{
		Id:      aws.String(distributionID),
		IfMatch: getResult.ETag,
	}
	
	_, err = s.client.DeleteDistribution(context.TODO(), deleteInput)
	if err != nil {
		return fmt.Errorf("failed to delete distribution: %v", err)
	}

	return nil
}

// CacheBehavior 缓存行为
type CacheBehavior struct {
	PathPattern string `json:"path_pattern"`
	TTL         int64  `json:"ttl"`
	MinTTL      int64  `json:"min_ttl"`
	MaxTTL      int64  `json:"max_ttl"`
}

// GetCacheBehaviors 获取缓存行为
func (s *AWSService) GetCacheBehaviors(distributionID string) ([]CacheBehavior, error) {
	input := &cloudfront.GetDistributionInput{
		Id: aws.String(distributionID),
	}
	
	result, err := s.client.GetDistribution(context.TODO(), input)
	if err != nil {
		return nil, fmt.Errorf("failed to get distribution: %v", err)
	}

	var behaviors []CacheBehavior
	
	// 默认缓存行为
	if result.Distribution.DistributionConfig.DefaultCacheBehavior != nil {
		defaultBehavior := result.Distribution.DistributionConfig.DefaultCacheBehavior
		behavior := CacheBehavior{
			PathPattern: "*",
			MinTTL:      *defaultBehavior.MinTTL,
		}
		if defaultBehavior.DefaultTTL != nil {
			behavior.TTL = *defaultBehavior.DefaultTTL
		}
		if defaultBehavior.MaxTTL != nil {
			behavior.MaxTTL = *defaultBehavior.MaxTTL
		}
		behaviors = append(behaviors, behavior)
	}

	// 其他缓存行为
	if result.Distribution.DistributionConfig.CacheBehaviors != nil {
		for _, cb := range result.Distribution.DistributionConfig.CacheBehaviors.Items {
			behavior := CacheBehavior{
				PathPattern: *cb.PathPattern,
				MinTTL:      *cb.MinTTL,
			}
			if cb.DefaultTTL != nil {
				behavior.TTL = *cb.DefaultTTL
			}
			if cb.MaxTTL != nil {
				behavior.MaxTTL = *cb.MaxTTL
			}
			behaviors = append(behaviors, behavior)
		}
	}

	return behaviors, nil
}

// InvalidateCache 刷新缓存
func (s *AWSService) InvalidateCache(distributionID string, paths []string) (string, error) {
	callerReference := fmt.Sprintf("invalidation-%d", time.Now().Unix())
	
	input := &cloudfront.CreateInvalidationInput{
		DistributionId: aws.String(distributionID),
		InvalidationBatch: &types.InvalidationBatch{
			CallerReference: &callerReference,
			Paths: &types.Paths{
				Quantity: aws.Int32(int32(len(paths))),
				Items:    paths,
			},
		},
	}

	result, err := s.client.CreateInvalidation(context.TODO(), input)
	if err != nil {
		return "", fmt.Errorf("failed to create invalidation: %v", err)
	}

	return *result.Invalidation.Id, nil
}

// GetInvalidationStatus 获取刷新状态
func (s *AWSService) GetInvalidationStatus(distributionID, invalidationID string) (string, error) {
	input := &cloudfront.GetInvalidationInput{
		DistributionId: aws.String(distributionID),
		Id:             aws.String(invalidationID),
	}

	result, err := s.client.GetInvalidation(context.TODO(), input)
	if err != nil {
		return "", fmt.Errorf("failed to get invalidation status: %v", err)
	}

	return string(result.Invalidation.Status), nil
}

// StatsData 统计数据
type StatsData struct {
	Date      time.Time `json:"date"`
	Requests  int64     `json:"requests"`
	Bandwidth int64     `json:"bandwidth"`
	HitRate   float64   `json:"hit_rate"`
}

// GetStats 获取统计数据
func (s *AWSService) GetStats(distributionID string, startTime, endTime time.Time) ([]StatsData, error) {
	// CloudFront统计数据需要通过CloudWatch获取
	// 这里简化实现，实际应该调用CloudWatch API
	
	// 模拟返回数据
	var stats []StatsData
	current := startTime
	for current.Before(endTime) {
		stats = append(stats, StatsData{
			Date:      current,
			Requests:  1000,
			Bandwidth: 1024 * 1024 * 100, // 100MB
			HitRate:   0.85,
		})
		current = current.AddDate(0, 0, 1)
	}

	return stats, nil
}

// SyncDistributionToDB 同步分发到数据库
func (s *AWSService) SyncDistributionToDB(accountID uint, dist DistributionInfo) (*models.CDNDomain, error) {
	config, _ := json.Marshal(map[string]interface{}{
		"distribution_id": dist.ID,
		"cnames":          dist.CNAME,
		"enabled":         dist.Enabled,
	})

	var domain string
	if len(dist.CNAME) > 0 {
		domain = dist.CNAME[0]
	} else {
		domain = dist.Domain
	}

	cdnDomain := &models.CDNDomain{
		AccountID:  accountID,
		Domain:     domain,
		Provider:   models.ProviderAWS,
		ProviderID: dist.ID,
		Status:     dist.Status,
		Origin:     dist.Origin,
		CNAME:      dist.Domain,
		Config:     string(config),
		IsActive:   dist.Enabled,
	}

	return cdnDomain, nil
}
