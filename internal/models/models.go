package models

import (
	"time"
	"gorm.io/gorm"
)

// CDNProvider CDN提供商类型
type CDNProvider string

const (
	ProviderTencent    CDNProvider = "tencent"
	ProviderAWS        CDNProvider = "aws"
	ProviderCloudflare CDNProvider = "cloudflare"
)

// User 用户模型
type User struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	Username  string         `json:"username" gorm:"uniqueIndex;not null"`
	Email     string         `json:"email" gorm:"uniqueIndex;not null"`
	Password  string         `json:"-" gorm:"not null"`
	Role      string         `json:"role" gorm:"default:user"`
	IsActive  bool           `json:"is_active" gorm:"default:true"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// CDNAccount CDN账户配置
type CDNAccount struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	UserID      uint           `json:"user_id" gorm:"not null"`
	Provider    CDNProvider    `json:"provider" gorm:"not null"`
	Name        string         `json:"name" gorm:"not null"`
	Credentials string         `json:"-" gorm:"type:text"` // 加密存储的凭证信息
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	User    User        `json:"user" gorm:"foreignKey:UserID"`
	Domains []CDNDomain `json:"domains" gorm:"foreignKey:AccountID"`
}

// CDNDomain CDN域名配置
type CDNDomain struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	AccountID   uint           `json:"account_id" gorm:"not null"`
	Domain      string         `json:"domain" gorm:"not null"`
	Provider    CDNProvider    `json:"provider" gorm:"not null"`
	ProviderID  string         `json:"provider_id"` // 在CDN提供商中的ID
	Status      string         `json:"status" gorm:"default:pending"`
	Origin      string         `json:"origin"`
	CNAME       string         `json:"cname"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	Config      string         `json:"config" gorm:"type:text"` // JSON格式的配置信息
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	Account CDNAccount    `json:"account" gorm:"foreignKey:AccountID"`
	Rules   []CacheRule   `json:"rules" gorm:"foreignKey:DomainID"`
	Stats   []DomainStats `json:"stats" gorm:"foreignKey:DomainID"`
}

// CacheRule 缓存规则
type CacheRule struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	DomainID  uint           `json:"domain_id" gorm:"not null"`
	Name      string         `json:"name" gorm:"not null"`
	Pattern   string         `json:"pattern" gorm:"not null"` // 匹配模式
	TTL       int            `json:"ttl"`                     // 缓存时间(秒)
	Priority  int            `json:"priority" gorm:"default:0"`
	IsActive  bool           `json:"is_active" gorm:"default:true"`
	Config    string         `json:"config" gorm:"type:text"` // 额外配置
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	Domain CDNDomain `json:"domain" gorm:"foreignKey:DomainID"`
}

// DomainStats 域名统计数据
type DomainStats struct {
	ID           uint           `json:"id" gorm:"primarykey"`
	DomainID     uint           `json:"domain_id" gorm:"not null"`
	Date         time.Time      `json:"date" gorm:"index"`
	Requests     int64          `json:"requests"`
	Bandwidth    int64          `json:"bandwidth"`    // 字节
	HitRate      float64        `json:"hit_rate"`     // 命中率
	StatusCode2xx int64         `json:"status_2xx"`
	StatusCode3xx int64         `json:"status_3xx"`
	StatusCode4xx int64         `json:"status_4xx"`
	StatusCode5xx int64         `json:"status_5xx"`
	TopCountries string         `json:"top_countries" gorm:"type:text"` // JSON格式
	TopReferers  string         `json:"top_referers" gorm:"type:text"`  // JSON格式
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	Domain CDNDomain `json:"domain" gorm:"foreignKey:DomainID"`
}

// PurgeTask 刷新任务
type PurgeTask struct {
	ID         uint           `json:"id" gorm:"primarykey"`
	DomainID   uint           `json:"domain_id" gorm:"not null"`
	UserID     uint           `json:"user_id" gorm:"not null"`
	Type       string         `json:"type" gorm:"not null"` // url, directory, all
	URLs       string         `json:"urls" gorm:"type:text"` // JSON数组
	Status     string         `json:"status" gorm:"default:pending"`
	ProviderID string         `json:"provider_id"` // 提供商任务ID
	Message    string         `json:"message"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	Domain CDNDomain `json:"domain" gorm:"foreignKey:DomainID"`
	User   User      `json:"user" gorm:"foreignKey:UserID"`
}

// APIKey API密钥管理
type APIKey struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	UserID      uint           `json:"user_id" gorm:"not null"`
	Name        string         `json:"name" gorm:"not null"`
	Key         string         `json:"key" gorm:"uniqueIndex;not null"`
	Permissions string         `json:"permissions" gorm:"type:text"` // JSON格式权限列表
	LastUsedAt  *time.Time     `json:"last_used_at"`
	ExpiresAt   *time.Time     `json:"expires_at"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// OperationLog 操作日志
type OperationLog struct {
	ID        uint      `json:"id" gorm:"primarykey"`
	UserID    uint      `json:"user_id"`
	Action    string    `json:"action" gorm:"not null"`
	Resource  string    `json:"resource"`
	Details   string    `json:"details" gorm:"type:text"`
	IP        string    `json:"ip"`
	UserAgent string    `json:"user_agent"`
	CreatedAt time.Time `json:"created_at"`
	
	// 关联
	User User `json:"user" gorm:"foreignKey:UserID"`
}
