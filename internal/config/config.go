package config

import (
	"encoding/json"
	"fmt"
	"os"
)

// Config 应用配置结构
type Config struct {
	Server   ServerConfig   `json:"server"`
	Database DatabaseConfig `json:"database"`
	CDN      CDNConfig      `json:"cdn"`
	Auth     AuthConfig     `json:"auth"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `json:"port"`
	Mode string `json:"mode"` // development, production
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type     string `json:"type"`     // mysql, postgres, sqlite
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	Database string `json:"database"`
	SSLMode  string `json:"ssl_mode"`
}

// CDNConfig CDN提供商配置
type CDNConfig struct {
	Tencent    TencentConfig    `json:"tencent"`
	AWS        AWSConfig        `json:"aws"`
	Cloudflare CloudflareConfig `json:"cloudflare"`
}

// TencentConfig 腾讯云配置
type TencentConfig struct {
	SecretID  string `json:"secret_id"`
	SecretKey string `json:"secret_key"`
	Region    string `json:"region"`
}

// AWSConfig AWS配置
type AWSConfig struct {
	AccessKeyID     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
	Region          string `json:"region"`
}

// CloudflareConfig Cloudflare配置
type CloudflareConfig struct {
	APIToken string `json:"api_token"`
	Email    string `json:"email"`
	APIKey   string `json:"api_key"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	JWTSecret     string `json:"jwt_secret"`
	TokenExpiry   int    `json:"token_expiry"` // 小时
	RefreshExpiry int    `json:"refresh_expiry"` // 天
}

// Load 加载配置文件
func Load() (*Config, error) {
	configFile := os.Getenv("CONFIG_FILE")
	if configFile == "" {
		configFile = "config.json"
	}

	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		defaultConfig := getDefaultConfig()
		if err := saveConfig(configFile, defaultConfig); err != nil {
			return nil, fmt.Errorf("failed to create default config: %v", err)
		}
		return defaultConfig, nil
	}

	// 读取配置文件
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %v", err)
	}

	// 从环境变量覆盖配置
	overrideFromEnv(&config)

	return &config, nil
}

// getDefaultConfig 获取默认配置
func getDefaultConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port: "8080",
			Mode: "development",
		},
		Database: DatabaseConfig{
			Type:     "sqlite",
			Database: "cdn_manager.db",
		},
		Auth: AuthConfig{
			JWTSecret:     "your-jwt-secret-key",
			TokenExpiry:   24,
			RefreshExpiry: 7,
		},
	}
}

// saveConfig 保存配置到文件
func saveConfig(filename string, config *Config) error {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}
	return os.WriteFile(filename, data, 0644)
}

// overrideFromEnv 从环境变量覆盖配置
func overrideFromEnv(config *Config) {
	if port := os.Getenv("SERVER_PORT"); port != "" {
		config.Server.Port = port
	}
	if mode := os.Getenv("SERVER_MODE"); mode != "" {
		config.Server.Mode = mode
	}
	
	// 数据库配置
	if dbType := os.Getenv("DB_TYPE"); dbType != "" {
		config.Database.Type = dbType
	}
	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.Database.Host = dbHost
	}
	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		config.Database.Port = dbPort
	}
	if dbUser := os.Getenv("DB_USERNAME"); dbUser != "" {
		config.Database.Username = dbUser
	}
	if dbPass := os.Getenv("DB_PASSWORD"); dbPass != "" {
		config.Database.Password = dbPass
	}
	if dbName := os.Getenv("DB_DATABASE"); dbName != "" {
		config.Database.Database = dbName
	}

	// CDN配置
	if tencentID := os.Getenv("TENCENT_SECRET_ID"); tencentID != "" {
		config.CDN.Tencent.SecretID = tencentID
	}
	if tencentKey := os.Getenv("TENCENT_SECRET_KEY"); tencentKey != "" {
		config.CDN.Tencent.SecretKey = tencentKey
	}
	if tencentRegion := os.Getenv("TENCENT_REGION"); tencentRegion != "" {
		config.CDN.Tencent.Region = tencentRegion
	}

	if awsKeyID := os.Getenv("AWS_ACCESS_KEY_ID"); awsKeyID != "" {
		config.CDN.AWS.AccessKeyID = awsKeyID
	}
	if awsSecretKey := os.Getenv("AWS_SECRET_ACCESS_KEY"); awsSecretKey != "" {
		config.CDN.AWS.SecretAccessKey = awsSecretKey
	}
	if awsRegion := os.Getenv("AWS_REGION"); awsRegion != "" {
		config.CDN.AWS.Region = awsRegion
	}

	if cfToken := os.Getenv("CLOUDFLARE_API_TOKEN"); cfToken != "" {
		config.CDN.Cloudflare.APIToken = cfToken
	}
	if cfEmail := os.Getenv("CLOUDFLARE_EMAIL"); cfEmail != "" {
		config.CDN.Cloudflare.Email = cfEmail
	}
	if cfKey := os.Getenv("CLOUDFLARE_API_KEY"); cfKey != "" {
		config.CDN.Cloudflare.APIKey = cfKey
	}

	// 认证配置
	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		config.Auth.JWTSecret = jwtSecret
	}
}
