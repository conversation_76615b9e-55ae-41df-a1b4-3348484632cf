package api

import (
	"cdn-manager/internal/api/handlers"
	"cdn-manager/internal/config"
	"cdn-manager/pkg/auth"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes 设置API路由
func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config) {
	// 创建处理器
	authHandler := handlers.NewAuthHandler(db, cfg)
	accountHandler := handlers.NewAccountHandler(db, cfg)
	domainHandler := handlers.NewDomainHandler(db, cfg)
	statsHandler := handlers.NewStatsHandler(db, cfg)
	purgeHandler := handlers.NewPurgeHandler(db, cfg)

	// 公开路由
	public := router.Group("/api/v1")
	{
		// 认证相关
		public.POST("/auth/login", authHandler.Login)
		public.POST("/auth/register", authHandler.Register)
		public.POST("/auth/refresh", authHandler.RefreshToken)
		
		// 健康检查
		public.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "ok"})
		})
	}

	// 需要认证的路由
	protected := router.Group("/api/v1")
	protected.Use(auth.JWTMiddleware(cfg.Auth.JWTSecret))
	{
		// 用户管理
		users := protected.Group("/users")
		{
			users.GET("/profile", authHandler.GetProfile)
			users.PUT("/profile", authHandler.UpdateProfile)
			users.POST("/change-password", authHandler.ChangePassword)
		}

		// CDN账户管理
		accounts := protected.Group("/accounts")
		{
			accounts.GET("", accountHandler.ListAccounts)
			accounts.POST("", accountHandler.CreateAccount)
			accounts.GET("/:id", accountHandler.GetAccount)
			accounts.PUT("/:id", accountHandler.UpdateAccount)
			accounts.DELETE("/:id", accountHandler.DeleteAccount)
			accounts.POST("/:id/test", accountHandler.TestConnection)
			accounts.POST("/:id/sync", accountHandler.SyncDomains)
		}

		// CDN域名管理
		domains := protected.Group("/domains")
		{
			domains.GET("", domainHandler.ListDomains)
			domains.POST("", domainHandler.CreateDomain)
			domains.GET("/:id", domainHandler.GetDomain)
			domains.PUT("/:id", domainHandler.UpdateDomain)
			domains.DELETE("/:id", domainHandler.DeleteDomain)
			domains.POST("/:id/enable", domainHandler.EnableDomain)
			domains.POST("/:id/disable", domainHandler.DisableDomain)
			
			// 缓存规则管理
			domains.GET("/:id/rules", domainHandler.GetCacheRules)
			domains.POST("/:id/rules", domainHandler.CreateCacheRule)
			domains.PUT("/:id/rules/:rule_id", domainHandler.UpdateCacheRule)
			domains.DELETE("/:id/rules/:rule_id", domainHandler.DeleteCacheRule)
			
			// 域名配置
			domains.GET("/:id/config", domainHandler.GetDomainConfig)
			domains.PUT("/:id/config", domainHandler.UpdateDomainConfig)
		}

		// 统计数据
		stats := protected.Group("/stats")
		{
			stats.GET("/overview", statsHandler.GetOverview)
			stats.GET("/domains/:id", statsHandler.GetDomainStats)
			stats.GET("/domains/:id/realtime", statsHandler.GetRealtimeStats)
			stats.GET("/domains/:id/bandwidth", statsHandler.GetBandwidthStats)
			stats.GET("/domains/:id/requests", statsHandler.GetRequestStats)
			stats.GET("/domains/:id/status-codes", statsHandler.GetStatusCodeStats)
			stats.GET("/domains/:id/countries", statsHandler.GetCountryStats)
			stats.GET("/domains/:id/referers", statsHandler.GetRefererStats)
		}

		// 缓存刷新
		purge := protected.Group("/purge")
		{
			purge.POST("/url", purgeHandler.PurgeByURL)
			purge.POST("/directory", purgeHandler.PurgeByDirectory)
			purge.POST("/all", purgeHandler.PurgeAll)
			purge.GET("/tasks", purgeHandler.GetPurgeTasks)
			purge.GET("/tasks/:id", purgeHandler.GetPurgeTask)
			purge.DELETE("/tasks/:id", purgeHandler.CancelPurgeTask)
		}

		// API密钥管理
		apiKeys := protected.Group("/api-keys")
		{
			apiKeys.GET("", authHandler.ListAPIKeys)
			apiKeys.POST("", authHandler.CreateAPIKey)
			apiKeys.PUT("/:id", authHandler.UpdateAPIKey)
			apiKeys.DELETE("/:id", authHandler.DeleteAPIKey)
		}

		// 操作日志
		logs := protected.Group("/logs")
		{
			logs.GET("", authHandler.GetOperationLogs)
		}
	}

	// 管理员路由
	admin := router.Group("/api/v1/admin")
	admin.Use(auth.JWTMiddleware(cfg.Auth.JWTSecret))
	admin.Use(auth.AdminMiddleware())
	{
		// 用户管理
		admin.GET("/users", authHandler.ListUsers)
		admin.POST("/users", authHandler.CreateUser)
		admin.PUT("/users/:id", authHandler.UpdateUser)
		admin.DELETE("/users/:id", authHandler.DeleteUser)
		admin.POST("/users/:id/reset-password", authHandler.ResetPassword)
		
		// 系统统计
		admin.GET("/stats/system", statsHandler.GetSystemStats)
		admin.GET("/stats/users", statsHandler.GetUserStats)
		admin.GET("/stats/domains", statsHandler.GetDomainStats)
	}

	// WebHook路由（用于CDN提供商回调）
	webhook := router.Group("/webhook")
	{
		webhook.POST("/tencent", domainHandler.TencentWebhook)
		webhook.POST("/aws", domainHandler.AWSWebhook)
		webhook.POST("/cloudflare", domainHandler.CloudflareWebhook)
	}
}
