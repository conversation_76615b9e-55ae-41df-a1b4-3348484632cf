<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDN管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-cloud-arrow-up"></i>
                    CDN管理平台
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="#dashboard" @click="currentView = 'dashboard'">
                                <i class="bi bi-speedometer2"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#domains" @click="currentView = 'domains'">
                                <i class="bi bi-globe"></i> 域名管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#accounts" @click="currentView = 'accounts'">
                                <i class="bi bi-person-gear"></i> 账户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#stats" @click="currentView = 'stats'">
                                <i class="bi bi-graph-up"></i> 统计分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#purge" @click="currentView = 'purge'">
                                <i class="bi bi-arrow-clockwise"></i> 缓存刷新
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> {{ user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#profile" @click="currentView = 'profile'">个人资料</a></li>
                                <li><a class="dropdown-item" href="#settings" @click="currentView = 'settings'">设置</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" @click="logout">退出登录</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <div class="container-fluid main-content">
            <div class="row">
                <!-- 侧边栏 -->
                <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" :class="{ active: currentView === 'dashboard' }" 
                                   href="#" @click="currentView = 'dashboard'">
                                    <i class="bi bi-speedometer2"></i> 概览
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" :class="{ active: currentView === 'domains' }" 
                                   href="#" @click="currentView = 'domains'">
                                    <i class="bi bi-globe"></i> 域名管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" :class="{ active: currentView === 'accounts' }" 
                                   href="#" @click="currentView = 'accounts'">
                                    <i class="bi bi-person-gear"></i> CDN账户
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" :class="{ active: currentView === 'stats' }" 
                                   href="#" @click="currentView = 'stats'">
                                    <i class="bi bi-graph-up"></i> 统计分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" :class="{ active: currentView === 'purge' }" 
                                   href="#" @click="currentView = 'purge'">
                                    <i class="bi bi-arrow-clockwise"></i> 缓存刷新
                                </a>
                            </li>
                        </ul>

                        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                            <span>快速操作</span>
                        </h6>
                        <ul class="nav flex-column mb-2">
                            <li class="nav-item">
                                <a class="nav-link" href="#" @click="showAddDomainModal">
                                    <i class="bi bi-plus-circle"></i> 添加域名
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" @click="showPurgeModal">
                                    <i class="bi bi-arrow-clockwise"></i> 快速刷新
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>

                <!-- 主要内容 -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                    <!-- 仪表板视图 -->
                    <div v-if="currentView === 'dashboard'" class="dashboard-view">
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">仪表板</h1>
                            <div class="btn-toolbar mb-2 mb-md-0">
                                <div class="btn-group me-2">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" @click="refreshDashboard">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 统计卡片 -->
                        <div class="row">
                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card border-left-primary shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                    总域名数
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.totalDomains }}</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-globe fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card border-left-success shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                    今日请求数
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ formatNumber(stats.todayRequests) }}</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card border-left-info shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                    今日带宽
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ formatBytes(stats.todayBandwidth) }}</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-speedometer2 fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card border-left-warning shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                    平均命中率
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.avgHitRate }}%</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-bullseye fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 图表区域 -->
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="card shadow mb-4">
                                    <div class="card-header py-3">
                                        <h6 class="m-0 font-weight-bold text-primary">请求趋势</h6>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="requestChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="card shadow mb-4">
                                    <div class="card-header py-3">
                                        <h6 class="m-0 font-weight-bold text-primary">CDN提供商分布</h6>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="providerChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 最近活动 -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">最近活动</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>操作</th>
                                                <th>资源</th>
                                                <th>详情</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="log in recentLogs" :key="log.id">
                                                <td>{{ formatTime(log.created_at) }}</td>
                                                <td>{{ log.action }}</td>
                                                <td>{{ log.resource }}</td>
                                                <td>{{ log.details }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 其他视图组件将在这里加载 -->
                    <div v-if="currentView === 'domains'" class="domains-view">
                        <!-- 域名管理视图 -->
                    </div>

                    <div v-if="currentView === 'accounts'" class="accounts-view">
                        <!-- 账户管理视图 -->
                    </div>

                    <div v-if="currentView === 'stats'" class="stats-view">
                        <!-- 统计分析视图 -->
                    </div>

                    <div v-if="currentView === 'purge'" class="purge-view">
                        <!-- 缓存刷新视图 -->
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- 自定义JS -->
    <script src="/static/js/app.js"></script>
</body>
</html>
