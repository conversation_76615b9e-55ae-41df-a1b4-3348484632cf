/* 全局样式 */
body {
    font-size: 0.875rem;
    background-color: #f8f9fc;
}

.feather {
    width: 16px;
    height: 16px;
    vertical-align: text-bottom;
}

/* 导航栏样式 */
.navbar-brand {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    font-size: 1rem;
    background-color: rgba(0, 0, 0, 0.25);
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);
}

.navbar .navbar-toggler {
    top: 0.25rem;
    right: 1rem;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 48px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: 0.5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
}

.sidebar .nav-link .feather {
    margin-right: 4px;
    color: #727272;
}

.sidebar .nav-link.active {
    color: #007bff;
}

.sidebar .nav-link:hover .feather,
.sidebar .nav-link.active .feather {
    color: inherit;
}

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
}

/* 主内容区域 */
.main-content {
    margin-top: 48px;
}

@media (max-width: 767.98px) {
    .main-content {
        margin-top: 5rem;
    }
}

/* 卡片样式 */
.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    border: none;
}

.card .card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* 边框颜色样式 */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

/* 文本颜色 */
.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* 字体样式 */
.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

/* 表格样式 */
.table {
    color: #858796;
}

.table th {
    background-color: #f8f9fc;
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.65rem;
    padding: 0.75rem;
}

.table td {
    font-size: 0.875rem;
    padding: 0.75rem;
}

/* 按钮样式 */
.btn {
    border-radius: 0.35rem;
    font-weight: 400;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-warning {
    background-color: #f6c23e;
    border-color: #f6c23e;
}

.btn-danger {
    background-color: #e74a3b;
    border-color: #e74a3b;
}

/* 表单样式 */
.form-control {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* 模态框样式 */
.modal-header {
    background-color: #4e73df;
    color: white;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 状态标签 */
.badge {
    font-size: 0.65rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
}

.badge-success {
    background-color: #1cc88a;
}

.badge-warning {
    background-color: #f6c23e;
}

.badge-danger {
    background-color: #e74a3b;
}

.badge-info {
    background-color: #36b9cc;
}

.badge-secondary {
    background-color: #858796;
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 0;
    }
    
    .main-content {
        margin-top: 0;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* 工具提示 */
.tooltip {
    font-size: 0.75rem;
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #4e73df;
    border-color: #dddfeb;
}

.page-link:hover {
    color: #224abe;
    background-color: #eaecf4;
    border-color: #dddfeb;
}

.page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-left: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #858796;
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background-color: #1cc88a;
}

.status-indicator.offline {
    background-color: #e74a3b;
}

.status-indicator.pending {
    background-color: #f6c23e;
}

/* 进度条样式 */
.progress {
    height: 0.5rem;
    border-radius: 0.35rem;
}

.progress-bar {
    border-radius: 0.35rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
