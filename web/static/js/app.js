// Vue.js 应用
const { createApp } = Vue;

// API 基础配置
const API_BASE_URL = '/api/v1';
let authToken = localStorage.getItem('authToken');

// Axios 配置
axios.defaults.baseURL = API_BASE_URL;
if (authToken) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
}

// 响应拦截器
axios.interceptors.response.use(
    response => response,
    error => {
        if (error.response && error.response.status === 401) {
            // 令牌过期，跳转到登录页
            localStorage.removeItem('authToken');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

// 主应用
const app = createApp({
    data() {
        return {
            currentView: 'dashboard',
            user: {
                username: '',
                email: '',
                role: ''
            },
            stats: {
                totalDomains: 0,
                todayRequests: 0,
                todayBandwidth: 0,
                avgHitRate: 0
            },
            domains: [],
            accounts: [],
            recentLogs: [],
            loading: false,
            charts: {
                request: null,
                provider: null
            }
        }
    },
    
    mounted() {
        this.init();
    },
    
    methods: {
        async init() {
            try {
                await this.loadUserProfile();
                await this.loadDashboardData();
                this.initCharts();
            } catch (error) {
                console.error('初始化失败:', error);
                this.showError('初始化失败，请刷新页面重试');
            }
        },
        
        async loadUserProfile() {
            try {
                const response = await axios.get('/users/profile');
                this.user = response.data;
            } catch (error) {
                console.error('加载用户信息失败:', error);
            }
        },
        
        async loadDashboardData() {
            this.loading = true;
            try {
                // 并行加载数据
                const [statsResponse, domainsResponse, logsResponse] = await Promise.all([
                    axios.get('/stats/overview'),
                    axios.get('/domains?limit=10'),
                    axios.get('/logs?limit=10')
                ]);
                
                this.stats = statsResponse.data;
                this.domains = domainsResponse.data.domains || [];
                this.recentLogs = logsResponse.data.logs || [];
                
                // 更新图表
                this.updateCharts();
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
                this.showError('加载数据失败');
            } finally {
                this.loading = false;
            }
        },
        
        async refreshDashboard() {
            await this.loadDashboardData();
            this.showSuccess('数据已刷新');
        },
        
        initCharts() {
            // 初始化请求趋势图表
            const requestCtx = document.getElementById('requestChart');
            if (requestCtx) {
                this.charts.request = new Chart(requestCtx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '请求数',
                            data: [],
                            borderColor: '#4e73df',
                            backgroundColor: 'rgba(78, 115, 223, 0.1)',
                            tension: 0.3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            // 初始化CDN提供商分布图表
            const providerCtx = document.getElementById('providerChart');
            if (providerCtx) {
                this.charts.provider = new Chart(providerCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['腾讯云', 'AWS', 'Cloudflare'],
                        datasets: [{
                            data: [0, 0, 0],
                            backgroundColor: [
                                '#4e73df',
                                '#1cc88a',
                                '#36b9cc'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        },
        
        updateCharts() {
            // 更新请求趋势图表
            if (this.charts.request && this.stats.requestTrend) {
                this.charts.request.data.labels = this.stats.requestTrend.map(item => item.date);
                this.charts.request.data.datasets[0].data = this.stats.requestTrend.map(item => item.requests);
                this.charts.request.update();
            }
            
            // 更新CDN提供商分布图表
            if (this.charts.provider && this.stats.providerDistribution) {
                this.charts.provider.data.datasets[0].data = [
                    this.stats.providerDistribution.tencent || 0,
                    this.stats.providerDistribution.aws || 0,
                    this.stats.providerDistribution.cloudflare || 0
                ];
                this.charts.provider.update();
            }
        },
        
        async logout() {
            try {
                localStorage.removeItem('authToken');
                delete axios.defaults.headers.common['Authorization'];
                window.location.href = '/login';
            } catch (error) {
                console.error('退出登录失败:', error);
            }
        },
        
        showAddDomainModal() {
            // 显示添加域名模态框
            this.currentView = 'domains';
            // TODO: 实现模态框显示逻辑
        },
        
        showPurgeModal() {
            // 显示快速刷新模态框
            this.currentView = 'purge';
            // TODO: 实现模态框显示逻辑
        },
        
        // 工具函数
        formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        },
        
        formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        formatTime(timestamp) {
            return new Date(timestamp).toLocaleString('zh-CN');
        },
        
        showSuccess(message) {
            // TODO: 实现成功提示
            console.log('Success:', message);
        },
        
        showError(message) {
            // TODO: 实现错误提示
            console.error('Error:', message);
        },
        
        showWarning(message) {
            // TODO: 实现警告提示
            console.warn('Warning:', message);
        }
    }
});

// 挂载应用
app.mount('#app');

// 全局错误处理
window.addEventListener('error', function(event) {
    console.error('全局错误:', event.error);
});

// 页面可见性变化时刷新数据
document.addEventListener('visibilitychange', function() {
    if (!document.hidden && app.currentView === 'dashboard') {
        app.loadDashboardData();
    }
});

// 定期刷新数据（每5分钟）
setInterval(() => {
    if (app.currentView === 'dashboard') {
        app.loadDashboardData();
    }
}, 5 * 60 * 1000);
