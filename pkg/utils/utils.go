package utils

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// GenerateAPIKey 生成API密钥
func GenerateAPIKey() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return "cdnm_" + hex.EncodeToString(bytes)
}

// JSONMarshal JSON序列化
func JSONMarshal(v interface{}) ([]byte, error) {
	return json.Marshal(v)
}

// JSONUnmarshal JSON反序列化
func JSONUnmarshal(data []byte, v interface{}) error {
	return json.Unmarshal(data, v)
}

// FormatBytes 格式化字节数
func FormatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// FormatDuration 格式化时间间隔
func FormatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0fs", d.Seconds())
	}
	if d < time.Hour {
		return fmt.Sprintf("%.0fm", d.Minutes())
	}
	if d < 24*time.Hour {
		return fmt.Sprintf("%.1fh", d.Hours())
	}
	return fmt.Sprintf("%.1fd", d.Hours()/24)
}

// ValidateDomain 验证域名格式
func ValidateDomain(domain string) bool {
	if len(domain) == 0 || len(domain) > 253 {
		return false
	}
	
	// 移除协议前缀
	domain = strings.TrimPrefix(domain, "http://")
	domain = strings.TrimPrefix(domain, "https://")
	
	// 移除路径
	if idx := strings.Index(domain, "/"); idx != -1 {
		domain = domain[:idx]
	}
	
	// 移除端口
	if idx := strings.Index(domain, ":"); idx != -1 {
		domain = domain[:idx]
	}
	
	// 检查域名格式
	parts := strings.Split(domain, ".")
	if len(parts) < 2 {
		return false
	}
	
	for _, part := range parts {
		if len(part) == 0 || len(part) > 63 {
			return false
		}
		
		// 检查字符
		for _, char := range part {
			if !((char >= 'a' && char <= 'z') || 
				 (char >= 'A' && char <= 'Z') || 
				 (char >= '0' && char <= '9') || 
				 char == '-') {
				return false
			}
		}
		
		// 不能以-开头或结尾
		if strings.HasPrefix(part, "-") || strings.HasSuffix(part, "-") {
			return false
		}
	}
	
	return true
}

// ValidateURL 验证URL格式
func ValidateURL(url string) bool {
	if len(url) == 0 {
		return false
	}
	
	// 简单的URL验证
	return strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://")
}

// SanitizeString 清理字符串
func SanitizeString(s string) string {
	// 移除前后空格
	s = strings.TrimSpace(s)
	
	// 移除特殊字符
	s = strings.ReplaceAll(s, "\n", "")
	s = strings.ReplaceAll(s, "\r", "")
	s = strings.ReplaceAll(s, "\t", "")
	
	return s
}

// Contains 检查切片是否包含元素
func Contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// RemoveDuplicates 移除切片中的重复元素
func RemoveDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string
	
	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}
	
	return result
}

// Paginate 分页计算
func Paginate(page, limit int) (offset int, actualLimit int) {
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	
	offset = (page - 1) * limit
	return offset, limit
}

// CalculateHitRate 计算命中率
func CalculateHitRate(hits, total int64) float64 {
	if total == 0 {
		return 0
	}
	return float64(hits) / float64(total) * 100
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// GetTimeRange 获取时间范围
func GetTimeRange(period string) TimeRange {
	now := time.Now()
	
	switch period {
	case "1h":
		return TimeRange{
			Start: now.Add(-time.Hour),
			End:   now,
		}
	case "24h":
		return TimeRange{
			Start: now.Add(-24 * time.Hour),
			End:   now,
		}
	case "7d":
		return TimeRange{
			Start: now.AddDate(0, 0, -7),
			End:   now,
		}
	case "30d":
		return TimeRange{
			Start: now.AddDate(0, 0, -30),
			End:   now,
		}
	default:
		return TimeRange{
			Start: now.Add(-24 * time.Hour),
			End:   now,
		}
	}
}

// MaskSensitiveData 掩码敏感数据
func MaskSensitiveData(data string) string {
	if len(data) <= 8 {
		return strings.Repeat("*", len(data))
	}
	
	return data[:4] + strings.Repeat("*", len(data)-8) + data[len(data)-4:]
}

// GenerateRandomString 生成随机字符串
func GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	rand.Read(b)
	
	for i := range b {
		b[i] = charset[int(b[i])%len(charset)]
	}
	
	return string(b)
}
